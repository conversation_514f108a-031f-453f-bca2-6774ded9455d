version: '3.8'

services:
  # API Gateway
  api-gateway:
    build:
      context: ./backend/api-gateway
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - auth-service
      - media-service
      - notification-service
    networks:
      - bioscan-network
    restart: unless-stopped

  # Authentication Service
  auth-service:
    build:
      context: ./backend/auth-service
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - DATABASE_URL=${AUTH_DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - APPLE_CLIENT_ID=${APPLE_CLIENT_ID}
      - APPLE_PRIVATE_KEY=${APPLE_PRIVATE_KEY}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres-auth
      - redis
    networks:
      - bioscan-network
    restart: unless-stopped

  # Media Processing Service
  media-service:
    build:
      context: ./backend/media-service
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - DATABASE_URL=${MEDIA_DATABASE_URL}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - S3_BUCKET=${S3_BUCKET}
      - CLOUDFRONT_URL=${CLOUDFRONT_URL}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    depends_on:
      - postgres-media
      - redis
    networks:
      - bioscan-network
    restart: unless-stopped

  # Notification Service
  notification-service:
    build:
      context: ./backend/notification-service
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - DATABASE_URL=${NOTIFICATION_DATABASE_URL}
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres-notifications
      - redis
    networks:
      - bioscan-network
    restart: unless-stopped

  # Payment Service
  payment-service:
    build:
      context: ./backend/payment-service
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - DATABASE_URL=${PAYMENT_DATABASE_URL}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres-payments
      - redis
    networks:
      - bioscan-network
    restart: unless-stopped

  # Analytics Service
  analytics-service:
    build:
      context: ./backend/analytics-service
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - DATABASE_URL=${ANALYTICS_DATABASE_URL}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres-analytics
      - redis
    networks:
      - bioscan-network
    restart: unless-stopped

  # PostgreSQL Databases
  postgres-auth:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=bioscan_auth
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_auth_data:/var/lib/postgresql/data
    networks:
      - bioscan-network
    restart: unless-stopped

  postgres-media:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=bioscan_media
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_media_data:/var/lib/postgresql/data
    networks:
      - bioscan-network
    restart: unless-stopped

  postgres-notifications:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=bioscan_notifications
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_notifications_data:/var/lib/postgresql/data
    networks:
      - bioscan-network
    restart: unless-stopped

  postgres-payments:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=bioscan_payments
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_payments_data:/var/lib/postgresql/data
    networks:
      - bioscan-network
    restart: unless-stopped

  postgres-analytics:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=bioscan_analytics
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_analytics_data:/var/lib/postgresql/data
    networks:
      - bioscan-network
    restart: unless-stopped

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - bioscan-network
    restart: unless-stopped

  # Nginx for load balancing and SSL termination
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api-gateway
    networks:
      - bioscan-network
    restart: unless-stopped

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - bioscan-network
    restart: unless-stopped

  # Grafana for monitoring dashboards
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - bioscan-network
    restart: unless-stopped

volumes:
  postgres_auth_data:
  postgres_media_data:
  postgres_notifications_data:
  postgres_payments_data:
  postgres_analytics_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  bioscan-network:
    driver: bridge
