#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

console.log('🔧 Fixing dependency conflicts...');

// Read package.json
const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Add specific versions to fix conflicts
const fixedDependencies = {
  ...packageJson.dependencies,
  'ajv': '^8.12.0',
  'ajv-keywords': '^5.1.0'
};

// Update package.json
packageJson.dependencies = fixedDependencies;

// Add resolutions for yarn/npm compatibility
packageJson.resolutions = {
  'ajv': '^8.12.0',
  'ajv-keywords': '^5.1.0'
};

packageJson.overrides = {
  'ajv': '^8.12.0',
  'ajv-keywords': '^5.1.0'
};

// Write updated package.json
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

console.log('✅ Updated package.json with fixed dependencies');

// Clean install
try {
  console.log('🧹 Cleaning node_modules...');
  if (fs.existsSync('node_modules')) {
    execSync('rmdir /s /q node_modules', { stdio: 'inherit' });
  }
  
  if (fs.existsSync('package-lock.json')) {
    fs.unlinkSync('package-lock.json');
  }
  
  console.log('📦 Installing dependencies...');
  execSync('npm install --legacy-peer-deps', { stdio: 'inherit' });
  
  console.log('✅ Dependencies fixed successfully!');
} catch (error) {
  console.error('❌ Error during installation:', error.message);
  console.log('💡 Try running: npm install --legacy-peer-deps --force');
}
