import { FraudDetectionService } from '../../services/FraudDetectionService';
import { supabase } from '../../lib/supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Device from 'expo-device';
import * as Network from 'expo-network';

// Mock dependencies
jest.mock('../../lib/supabase');
jest.mock('@react-native-async-storage/async-storage');
jest.mock('expo-device');
jest.mock('expo-network');

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;
const mockDevice = Device as jest.Mocked<typeof Device>;
const mockNetwork = Network as jest.Mocked<typeof Network>;

describe('FraudDetectionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mocks
    mockDevice.brand = 'Apple';
    mockDevice.manufacturer = 'Apple';
    mockDevice.modelName = 'iPhone 14';
    mockDevice.osName = 'iOS';
    mockDevice.osVersion = '16.0';
    mockDevice.deviceType = 1; // Phone
    mockDevice.isDevice = true;

    mockNetwork.getNetworkStateAsync.mockResolvedValue({
      type: 'WIFI',
      isConnected: true,
      isInternetReachable: true
    } as any);

    mockSupabase.functions = {
      invoke: jest.fn()
    } as any;
  });

  describe('validatePayment', () => {
    it('should validate payment successfully with low risk', async () => {
      // Mock rate limit check
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify({
        hourly: [Date.now() - 30 * 60 * 1000], // 1 attempt 30 minutes ago
        daily: [Date.now() - 30 * 60 * 1000]
      }));

      // Mock server response
      const mockServerResponse = {
        riskScore: 25,
        requiresVerification: false,
        blocked: false,
        riskFactors: { low_amount: true }
      };

      mockSupabase.functions.invoke.mockResolvedValue({
        data: mockServerResponse,
        error: null
      });

      const result = await FraudDetectionService.validatePayment({
        paymentMethodId: 'pm_123',
        amount: 50,
        currency: 'USD'
      });

      expect(result).toEqual({
        isBlocked: false,
        riskScore: 25,
        riskFactors: ['low_amount'],
        requiresVerification: false
      });

      expect(mockSupabase.functions.invoke).toHaveBeenCalledWith('validate-payment', {
        body: expect.objectContaining({
          paymentMethodId: 'pm_123',
          amount: 50,
          currency: 'USD',
          deviceFingerprint: expect.any(String),
          networkInfo: expect.any(Object)
        })
      });
    });

    it('should block payment when rate limit exceeded', async () => {
      // Mock rate limit exceeded
      const now = Date.now();
      const recentAttempts = Array.from({ length: 6 }, (_, i) => now - i * 5 * 60 * 1000); // 6 attempts in last 30 minutes

      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify({
        hourly: recentAttempts,
        daily: recentAttempts
      }));

      const result = await FraudDetectionService.validatePayment({
        paymentMethodId: 'pm_123',
        amount: 50,
        currency: 'USD'
      });

      expect(result.isBlocked).toBe(true);
      expect(result.riskScore).toBe(100);
      expect(result.riskFactors).toContain('rate_limit_exceeded');
      expect(result.message).toContain('Too many payment attempts');

      // Should not call server when rate limited
      expect(mockSupabase.functions.invoke).not.toHaveBeenCalled();
    });

    it('should handle server validation errors gracefully', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null); // No previous attempts

      mockSupabase.functions.invoke.mockResolvedValue({
        data: null,
        error: new Error('Server error')
      });

      const result = await FraudDetectionService.validatePayment({
        paymentMethodId: 'pm_123',
        amount: 50,
        currency: 'USD'
      });

      expect(result).toEqual({
        isBlocked: false,
        riskScore: 50,
        riskFactors: ['validation_error'],
        requiresVerification: true,
        message: 'Unable to validate payment. Please try again.'
      });
    });

    it('should handle high-risk payments', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);

      const mockServerResponse = {
        riskScore: 85,
        requiresVerification: true,
        blocked: true,
        riskFactors: { 
          high_amount: true, 
          suspicious_country: true,
          multiple_recent_failures: 3
        }
      };

      mockSupabase.functions.invoke.mockResolvedValue({
        data: mockServerResponse,
        error: null
      });

      const result = await FraudDetectionService.validatePayment({
        paymentMethodId: 'pm_123',
        amount: 1000,
        currency: 'USD'
      });

      expect(result.isBlocked).toBe(true);
      expect(result.riskScore).toBe(85);
      expect(result.riskFactors).toEqual(['high_amount', 'suspicious_country', 'multiple_recent_failures']);
      expect(result.message).toBe('Payment blocked for security reasons.');
    });
  });

  describe('isPaymentAllowed', () => {
    it('should allow payment when under rate limits', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify({
        hourly: [Date.now() - 30 * 60 * 1000], // 1 attempt 30 minutes ago
        daily: [Date.now() - 30 * 60 * 1000]
      }));

      const result = await FraudDetectionService.isPaymentAllowed();

      expect(result).toBe(true);
    });

    it('should block payment when hourly rate limit exceeded', async () => {
      const now = Date.now();
      const recentAttempts = Array.from({ length: 6 }, (_, i) => now - i * 5 * 60 * 1000);

      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify({
        hourly: recentAttempts,
        daily: recentAttempts
      }));

      const result = await FraudDetectionService.isPaymentAllowed();

      expect(result).toBe(false);
    });

    it('should block payment when daily rate limit exceeded', async () => {
      const now = Date.now();
      const dailyAttempts = Array.from({ length: 21 }, (_, i) => now - i * 60 * 60 * 1000); // 21 attempts in last day

      mockAsyncStorage.getItem.mockResolvedValue(JSON.stringify({
        hourly: [now - 30 * 60 * 1000], // Only 1 in last hour
        daily: dailyAttempts
      }));

      const result = await FraudDetectionService.isPaymentAllowed();

      expect(result).toBe(false);
    });

    it('should allow payment on storage error', async () => {
      mockAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));

      const result = await FraudDetectionService.isPaymentAllowed();

      expect(result).toBe(true); // Fail open for legitimate users
    });
  });

  describe('generateDeviceFingerprint', () => {
    it('should generate consistent device fingerprint', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null); // No existing fingerprint

      const fingerprint1 = await (FraudDetectionService as any).generateDeviceFingerprint();
      
      // Mock that fingerprint is now stored
      mockAsyncStorage.getItem.mockResolvedValue(fingerprint1);
      
      const fingerprint2 = await (FraudDetectionService as any).generateDeviceFingerprint();

      expect(fingerprint1).toBe(fingerprint2);
      expect(fingerprint1).toMatch(/^[a-zA-Z0-9]{32}$/); // 32 character alphanumeric
    });

    it('should return existing fingerprint when available', async () => {
      const existingFingerprint = 'abc123def456ghi789jkl012mno345pq';
      mockAsyncStorage.getItem.mockResolvedValue(existingFingerprint);

      const fingerprint = await (FraudDetectionService as any).generateDeviceFingerprint();

      expect(fingerprint).toBe(existingFingerprint);
      expect(mockAsyncStorage.setItem).not.toHaveBeenCalled();
    });

    it('should handle device info errors gracefully', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);
      
      // Mock device info error
      Object.defineProperty(mockDevice, 'brand', {
        get: () => { throw new Error('Device info error'); }
      });

      const fingerprint = await (FraudDetectionService as any).generateDeviceFingerprint();

      expect(fingerprint).toBe('unknown_device');
    });
  });

  describe('getFraudStats', () => {
    it('should return fraud statistics', async () => {
      const mockStats = {
        deviceFingerprint: 'abc123',
        recentAttempts: 2,
        lastValidation: {
          timestamp: Date.now(),
          riskScore: 25
        }
      };

      mockAsyncStorage.multiGet.mockResolvedValue([
        ['device_fingerprint', 'abc123'],
        ['payment_attempts', JSON.stringify({ hourly: [Date.now() - 30 * 60 * 1000, Date.now() - 10 * 60 * 1000] })],
        ['last_validation', JSON.stringify(mockStats.lastValidation)]
      ]);

      const result = await FraudDetectionService.getFraudStats();

      expect(result.deviceFingerprint).toBe('abc123');
      expect(result.recentAttempts).toBe(2);
      expect(result.lastValidation).toEqual(mockStats.lastValidation);
    });

    it('should handle missing data gracefully', async () => {
      mockAsyncStorage.multiGet.mockResolvedValue([
        ['device_fingerprint', null],
        ['payment_attempts', null],
        ['last_validation', null]
      ]);

      const result = await FraudDetectionService.getFraudStats();

      expect(result).toEqual({
        deviceFingerprint: 'not_generated',
        recentAttempts: 0,
        lastValidation: undefined
      });
    });

    it('should handle storage errors', async () => {
      mockAsyncStorage.multiGet.mockRejectedValue(new Error('Storage error'));

      const result = await FraudDetectionService.getFraudStats();

      expect(result).toEqual({
        deviceFingerprint: 'error',
        recentAttempts: 0
      });
    });
  });

  describe('clearFraudData', () => {
    it('should clear all fraud detection data', async () => {
      await FraudDetectionService.clearFraudData();

      expect(mockAsyncStorage.multiRemove).toHaveBeenCalledWith([
        'device_fingerprint',
        'payment_attempts',
        'last_validation'
      ]);
    });

    it('should handle clear errors gracefully', async () => {
      mockAsyncStorage.multiRemove.mockRejectedValue(new Error('Clear error'));

      // Should not throw
      await expect(FraudDetectionService.clearFraudData()).resolves.toBeUndefined();
    });
  });
});
