import { PaymentService } from '../../services/PaymentService';
import { FraudDetectionService } from '../../services/FraudDetectionService';
import { SubscriptionManagementService } from '../../services/SubscriptionManagementService';
import { supabase } from '../../lib/supabase';

// Integration tests for complete payment flows
describe('Payment Flow Integration Tests', () => {
  let paymentService: PaymentService;
  
  beforeAll(async () => {
    paymentService = new PaymentService();
    
    // Setup test user authentication
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    if (error) {
      throw new Error(`Failed to authenticate test user: ${error.message}`);
    }
  });

  afterAll(async () => {
    // Cleanup: sign out test user
    await supabase.auth.signOut();
  });

  describe('Complete Subscription Flow', () => {
    it('should complete full subscription lifecycle', async () => {
      // 1. Get available plans
      const plans = await paymentService.getSubscriptionPlans();
      expect(plans.length).toBeGreaterThan(0);
      
      const proPlan = plans.find(plan => plan.name === 'Pro');
      expect(proPlan).toBeDefined();

      // 2. Check fraud detection allows payment
      const isAllowed = await FraudDetectionService.isPaymentAllowed();
      expect(isAllowed).toBe(true);

      // 3. Validate payment method (mock)
      const validation = await FraudDetectionService.validatePayment({
        paymentMethodId: 'pm_test_card',
        amount: proPlan!.price_monthly,
        currency: 'USD'
      });
      
      expect(validation.isBlocked).toBe(false);
      expect(validation.riskScore).toBeLessThan(70);

      // 4. Create checkout session
      const checkoutSession = await paymentService.createCheckoutSession(
        proPlan!.id,
        'monthly'
      );
      
      expect(checkoutSession.url).toContain('checkout.stripe.com');

      // 5. Simulate successful payment (in real test, this would be done via Stripe)
      // For integration test, we'll check that the subscription can be retrieved
      
      // 6. Check subscription status
      const subscription = await SubscriptionManagementService.getCurrentSubscription();
      // Note: In a real integration test, this would be the newly created subscription
      
      // 7. Check usage stats
      const usage = await SubscriptionManagementService.getUsageStats();
      expect(Array.isArray(usage)).toBe(true);

      // 8. Test feature usage tracking
      const canUseFeature = await SubscriptionManagementService.canUseFeature('identification');
      expect(typeof canUseFeature).toBe('boolean');

      if (canUseFeature) {
        await SubscriptionManagementService.trackUsage('identification', 1);
        
        // Verify usage was tracked
        const updatedUsage = await SubscriptionManagementService.getUsageStats();
        const identificationUsage = updatedUsage.find(u => u.feature_type === 'identification');
        expect(identificationUsage?.current_usage).toBeGreaterThan(0);
      }
    }, 30000); // 30 second timeout for integration test

    it('should handle plan changes correctly', async () => {
      const currentSub = await SubscriptionManagementService.getCurrentSubscription();
      
      if (!currentSub) {
        console.log('No active subscription for plan change test');
        return;
      }

      const plans = await paymentService.getSubscriptionPlans();
      const targetPlan = plans.find(plan => plan.id !== currentSub.plan_id);
      
      if (!targetPlan) {
        console.log('No alternative plan available for change test');
        return;
      }

      // Preview plan change
      const preview = await SubscriptionManagementService.previewPlanChange(
        targetPlan.id,
        'monthly'
      );
      
      expect(preview).toBeDefined();
      expect(preview?.new_plan.id).toBe(targetPlan.id);
      expect(preview?.current_plan.id).toBe(currentSub.plan_id);
      expect(typeof preview?.proration_amount).toBe('number');

      // In a real test, you would proceed with the plan change
      // const changeResult = await SubscriptionManagementService.changePlan(
      //   targetPlan.id,
      //   'monthly'
      // );
      // expect(changeResult.success).toBe(true);
    });

    it('should handle subscription cancellation and reactivation', async () => {
      const currentSub = await SubscriptionManagementService.getCurrentSubscription();
      
      if (!currentSub || currentSub.status !== 'active') {
        console.log('No active subscription for cancellation test');
        return;
      }

      // Cancel subscription
      const cancelResult = await SubscriptionManagementService.cancelSubscription(
        true, // cancel at period end
        'Testing cancellation flow'
      );
      
      expect(cancelResult.success).toBe(true);
      expect(cancelResult.message).toContain('end of the current billing period');

      // Verify subscription is marked for cancellation
      const cancelledSub = await SubscriptionManagementService.getCurrentSubscription();
      expect(cancelledSub?.cancel_at_period_end).toBe(true);

      // Reactivate subscription
      const reactivateResult = await SubscriptionManagementService.reactivateSubscription();
      expect(reactivateResult.success).toBe(true);

      // Verify subscription is reactivated
      const reactivatedSub = await SubscriptionManagementService.getCurrentSubscription();
      expect(reactivatedSub?.cancel_at_period_end).toBe(false);
    });
  });

  describe('Fraud Detection Integration', () => {
    it('should detect and handle suspicious payment patterns', async () => {
      // Clear any existing fraud data
      await FraudDetectionService.clearFraudData();

      // Simulate multiple rapid payment attempts
      const attempts = [];
      for (let i = 0; i < 6; i++) {
        attempts.push(
          FraudDetectionService.validatePayment({
            paymentMethodId: 'pm_test_card',
            amount: 100,
            currency: 'USD'
          })
        );
      }

      const results = await Promise.all(attempts);

      // First few attempts should be allowed
      expect(results[0].isBlocked).toBe(false);
      expect(results[1].isBlocked).toBe(false);

      // Later attempts should be blocked due to rate limiting
      const blockedResults = results.filter(r => r.isBlocked);
      expect(blockedResults.length).toBeGreaterThan(0);

      // Check that payment is now blocked
      const isAllowed = await FraudDetectionService.isPaymentAllowed();
      expect(isAllowed).toBe(false);

      // Get fraud stats
      const stats = await FraudDetectionService.getFraudStats();
      expect(stats.recentAttempts).toBeGreaterThan(5);
      expect(stats.deviceFingerprint).toBeDefined();
    });

    it('should handle high-risk payment scenarios', async () => {
      // Test high-amount payment
      const highAmountValidation = await FraudDetectionService.validatePayment({
        paymentMethodId: 'pm_test_card',
        amount: 1000, // High amount
        currency: 'USD'
      });

      expect(highAmountValidation.riskScore).toBeGreaterThan(0);
      expect(highAmountValidation.riskFactors).toContain('high_amount');

      // Test very high amount that should require verification
      const veryHighAmountValidation = await FraudDetectionService.validatePayment({
        paymentMethodId: 'pm_test_card',
        amount: 5000,
        currency: 'USD'
      });

      expect(veryHighAmountValidation.requiresVerification).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      // Mock network error by using invalid plan ID
      await expect(
        paymentService.createCheckoutSession('invalid_plan_id', 'monthly')
      ).rejects.toThrow();
    });

    it('should handle authentication errors', async () => {
      // Sign out to test unauthenticated requests
      await supabase.auth.signOut();

      await expect(
        paymentService.getCurrentSubscription()
      ).resolves.toBeNull();

      // Re-authenticate for other tests
      await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'testpassword123'
      });
    });

    it('should handle invalid payment method validation', async () => {
      const validation = await FraudDetectionService.validatePayment({
        paymentMethodId: '', // Invalid payment method
        amount: 50,
        currency: 'USD'
      });

      expect(validation.isBlocked).toBe(false); // Should fail gracefully
      expect(validation.riskFactors).toContain('validation_error');
    });
  });

  describe('Performance Tests', () => {
    it('should handle concurrent payment validations', async () => {
      const concurrentValidations = Array.from({ length: 10 }, () =>
        FraudDetectionService.validatePayment({
          paymentMethodId: 'pm_test_card',
          amount: 50,
          currency: 'USD'
        })
      );

      const startTime = Date.now();
      const results = await Promise.all(concurrentValidations);
      const endTime = Date.now();

      // All validations should complete
      expect(results.length).toBe(10);
      results.forEach(result => {
        expect(result).toHaveProperty('isBlocked');
        expect(result).toHaveProperty('riskScore');
      });

      // Should complete within reasonable time (5 seconds)
      expect(endTime - startTime).toBeLessThan(5000);
    });

    it('should cache device fingerprint for performance', async () => {
      const startTime1 = Date.now();
      const stats1 = await FraudDetectionService.getFraudStats();
      const endTime1 = Date.now();

      const startTime2 = Date.now();
      const stats2 = await FraudDetectionService.getFraudStats();
      const endTime2 = Date.now();

      // Second call should be faster (cached)
      expect(endTime2 - startTime2).toBeLessThanOrEqual(endTime1 - startTime1);
      expect(stats1.deviceFingerprint).toBe(stats2.deviceFingerprint);
    });
  });
});
