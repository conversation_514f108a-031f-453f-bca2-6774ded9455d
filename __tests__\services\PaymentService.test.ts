import { PaymentService } from '../../services/PaymentService';
import { FraudDetectionService } from '../../services/FraudDetectionService';
import { supabase } from '../../lib/supabase';

// Mock dependencies
jest.mock('../../lib/supabase');
jest.mock('../../services/FraudDetectionService');

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockFraudDetectionService = FraudDetectionService as jest.Mocked<typeof FraudDetectionService>;

describe('PaymentService', () => {
  let paymentService: PaymentService;

  beforeEach(() => {
    paymentService = new PaymentService();
    jest.clearAllMocks();
  });

  describe('getSubscriptionPlans', () => {
    it('should fetch active subscription plans', async () => {
      const mockPlans = [
        {
          id: '1',
          name: 'Pro',
          price_monthly: 9.99,
          price_yearly: 99.99,
          features: ['feature1', 'feature2'],
          is_active: true
        }
      ];

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockPlans,
              error: null
            })
          })
        })
      } as any);

      const result = await paymentService.getSubscriptionPlans();

      expect(result).toEqual(mockPlans);
      expect(mockSupabase.from).toHaveBeenCalledWith('subscription_plans');
    });

    it('should handle errors when fetching plans', async () => {
      const mockError = new Error('Database error');

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: null,
              error: mockError
            })
          })
        })
      } as any);

      await expect(paymentService.getSubscriptionPlans()).rejects.toThrow('Database error');
    });
  });

  describe('createCheckoutSession', () => {
    beforeEach(() => {
      mockSupabase.auth = {
        getUser: jest.fn().mockResolvedValue({
          data: { user: { id: 'user123', email: '<EMAIL>' } },
          error: null
        })
      } as any;

      mockSupabase.functions = {
        invoke: jest.fn()
      } as any;
    });

    it('should create checkout session when payment is allowed', async () => {
      mockFraudDetectionService.isPaymentAllowed.mockResolvedValue(true);
      
      const mockResponse = { url: 'https://checkout.stripe.com/session123' };
      mockSupabase.functions.invoke.mockResolvedValue({
        data: mockResponse,
        error: null
      });

      const result = await paymentService.createCheckoutSession('plan123', 'monthly');

      expect(mockFraudDetectionService.isPaymentAllowed).toHaveBeenCalled();
      expect(mockSupabase.functions.invoke).toHaveBeenCalledWith('create-checkout-session', {
        body: {
          planId: 'plan123',
          billingPeriod: 'monthly',
          userId: 'user123',
          userEmail: '<EMAIL>',
          promoCode: undefined
        }
      });
      expect(result).toEqual(mockResponse);
    });

    it('should throw error when payment is blocked', async () => {
      mockFraudDetectionService.isPaymentAllowed.mockResolvedValue(false);

      await expect(
        paymentService.createCheckoutSession('plan123', 'monthly')
      ).rejects.toThrow('Payment temporarily blocked. Please try again later.');

      expect(mockSupabase.functions.invoke).not.toHaveBeenCalled();
    });

    it('should throw error when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      });

      await expect(
        paymentService.createCheckoutSession('plan123', 'monthly')
      ).rejects.toThrow('User not authenticated');
    });

    it('should include promo code when provided', async () => {
      mockFraudDetectionService.isPaymentAllowed.mockResolvedValue(true);
      
      const mockResponse = { url: 'https://checkout.stripe.com/session123' };
      mockSupabase.functions.invoke.mockResolvedValue({
        data: mockResponse,
        error: null
      });

      await paymentService.createCheckoutSession('plan123', 'yearly', 'PROMO20');

      expect(mockSupabase.functions.invoke).toHaveBeenCalledWith('create-checkout-session', {
        body: {
          planId: 'plan123',
          billingPeriod: 'yearly',
          userId: 'user123',
          userEmail: '<EMAIL>',
          promoCode: 'PROMO20'
        }
      });
    });
  });

  describe('validatePaymentMethod', () => {
    it('should validate payment method successfully', async () => {
      const mockValidationResult = {
        isBlocked: false,
        riskScore: 25,
        riskFactors: ['low_amount'],
        requiresVerification: false
      };

      mockFraudDetectionService.validatePayment.mockResolvedValue(mockValidationResult);

      const result = await paymentService.validatePaymentMethod('pm_123', 50, 'USD');

      expect(mockFraudDetectionService.validatePayment).toHaveBeenCalledWith({
        paymentMethodId: 'pm_123',
        amount: 50,
        currency: 'USD'
      });
      expect(result).toEqual(mockValidationResult);
    });

    it('should handle validation errors gracefully', async () => {
      mockFraudDetectionService.validatePayment.mockRejectedValue(new Error('Validation failed'));

      const result = await paymentService.validatePaymentMethod('pm_123', 50, 'USD');

      expect(result).toEqual({
        isBlocked: false,
        riskScore: 50,
        riskFactors: ['validation_error'],
        requiresVerification: true,
        message: 'Unable to validate payment method'
      });
    });
  });

  describe('getCurrentSubscription', () => {
    beforeEach(() => {
      mockSupabase.auth = {
        getUser: jest.fn().mockResolvedValue({
          data: { user: { id: 'user123' } },
          error: null
        })
      } as any;
    });

    it('should fetch current active subscription', async () => {
      const mockSubscription = {
        id: 'sub123',
        user_id: 'user123',
        status: 'active',
        plan: { name: 'Pro', price_monthly: 9.99 }
      };

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockSubscription,
              error: null
            })
          })
        })
      } as any);

      const result = await paymentService.getCurrentSubscription();

      expect(result).toEqual(mockSubscription);
      expect(mockSupabase.from).toHaveBeenCalledWith('user_subscriptions');
    });

    it('should return null when no subscription found', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { code: 'PGRST116' } // Not found error
            })
          })
        })
      } as any);

      const result = await paymentService.getCurrentSubscription();

      expect(result).toBeNull();
    });

    it('should return null when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      });

      const result = await paymentService.getCurrentSubscription();

      expect(result).toBeNull();
    });
  });

  describe('cancelSubscription', () => {
    beforeEach(() => {
      mockSupabase.auth = {
        getUser: jest.fn().mockResolvedValue({
          data: { user: { id: 'user123' } },
          error: null
        })
      } as any;

      mockSupabase.functions = {
        invoke: jest.fn()
      } as any;
    });

    it('should cancel subscription successfully', async () => {
      const mockResponse = { success: true };
      mockSupabase.functions.invoke.mockResolvedValue({
        data: mockResponse,
        error: null
      });

      const result = await paymentService.cancelSubscription('sub123');

      expect(mockSupabase.functions.invoke).toHaveBeenCalledWith('cancel-subscription', {
        body: { subscriptionId: 'sub123' }
      });
      expect(result).toEqual(mockResponse);
    });

    it('should handle cancellation errors', async () => {
      const mockError = new Error('Cancellation failed');
      mockSupabase.functions.invoke.mockResolvedValue({
        data: null,
        error: mockError
      });

      await expect(paymentService.cancelSubscription('sub123')).rejects.toThrow('Cancellation failed');
    });
  });

  describe('getUsageStats', () => {
    beforeEach(() => {
      mockSupabase.auth = {
        getUser: jest.fn().mockResolvedValue({
          data: { user: { id: 'user123' } },
          error: null
        })
      } as any;
    });

    it('should fetch usage statistics', async () => {
      const mockUsageStats = {
        current_period_identifications: 15,
        total_identifications: 150,
        remaining_identifications: 85,
        usage_percentage: 15,
        period_start: '2024-01-01',
        period_end: '2024-01-31'
      };

      mockSupabase.rpc = jest.fn().mockResolvedValue({
        data: mockUsageStats,
        error: null
      });

      const result = await paymentService.getUsageStats();

      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_user_usage_stats', {
        user_uuid: 'user123'
      });
      expect(result).toEqual(mockUsageStats);
    });

    it('should return default stats when RPC fails', async () => {
      mockSupabase.rpc = jest.fn().mockResolvedValue({
        data: null,
        error: new Error('RPC failed')
      });

      const result = await paymentService.getUsageStats();

      expect(result).toEqual({
        current_period_identifications: 0,
        total_identifications: 0,
        remaining_identifications: 0,
        usage_percentage: 0,
        period_start: expect.any(String),
        period_end: expect.any(String)
      });
    });
  });
});
