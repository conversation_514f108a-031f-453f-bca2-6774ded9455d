import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { ArrowLeft, Share, Heart, BookOpen, MapPin, Calendar, Ruler, Thermometer, Droplet, Sun, Eye, Camera, Star, CircleAlert as AlertCircle, CircleCheck as CheckCircle, Volume2, Leaf, Bug, Zap, Target, RotateCcw } from 'lucide-react-native';

const { width } = Dimensions.get('window');

export default function ScanResultScreen() {
  const router = useRouter();
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);

  const scanResult = {
    name: 'Monarch Butterfly',
    scientificName: 'Danaus plexippus',
    confidence: 96,
    category: 'Lepidoptera',
    conservationStatus: 'Vulnerable',
    rarity: 'Common',
    location: 'Central Park, New York',
    scanDate: new Date().toLocaleDateString(),
    description: 'The monarch butterfly is one of the most recognizable and well-studied butterflies in North America. Known for their incredible migration patterns, these butterflies can travel thousands of miles.',
    facts: [
      'Can migrate up to 3,000 miles',
      'Lives for 2-6 weeks (except migration generation)',
      'Uses milkweed plants exclusively for reproduction',
      'Population has declined by 80% in recent decades',
    ],
    habitat: {
      climate: 'Temperate',
      temperature: '15-25°C',
      humidity: '50-70%',
      sunlight: 'Full sun',
    },
    physicalTraits: {
      wingspan: '8.9-10.2 cm',
      weight: '0.25-0.75 g',
      lifespan: '2-6 weeks',
      diet: 'Nectar (adults), Milkweed (larvae)',
    },
  };

  const alternativeMatches = [
    { name: 'Queen Butterfly', scientificName: 'Danaus gilippus', confidence: 23 },
    { name: 'Viceroy Butterfly', scientificName: 'Limenitis archippus', confidence: 18 },
    { name: 'Soldier Butterfly', scientificName: 'Danaus eresimus', confidence: 12 },
  ];

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return '#22C55E';
    if (confidence >= 70) return '#F59E0B';
    return '#EF4444';
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'vulnerable': return '#F59E0B';
      case 'endangered': return '#EF4444';
      case 'critical': return '#DC2626';
      case 'least concern': return '#22C55E';
      default: return '#6B7280';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#FEF3C7', '#FDE68A', '#FCD34D']}
        style={styles.gradient}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => router.back()}>
            <ArrowLeft size={24} color="#111827" />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Scan Result</Text>
          
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.headerButton}>
              <Share size={20} color="#111827" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.headerButton}>
              <RotateCcw size={20} color="#111827" />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Specimen Image */}
          <View style={styles.imageContainer}>
            <LinearGradient
              colors={['#F59E0B', '#D97706']}
              style={styles.specimenImage}>
              <Bug size={80} color="#FFFFFF" />
            </LinearGradient>
            
            {/* Confidence Badge */}
            <View style={[
              styles.confidenceBadge,
              { backgroundColor: getConfidenceColor(scanResult.confidence) }
            ]}>
              <Target size={16} color="#FFFFFF" />
              <Text style={styles.confidenceText}>{scanResult.confidence}% Match</Text>
            </View>
          </View>

          {/* Primary Result */}
          <View style={styles.resultCard}>
            <View style={styles.resultHeader}>
              <View style={styles.resultTitleContainer}>
                <Text style={styles.resultName}>{scanResult.name}</Text>
                <Text style={styles.resultScientific}>{scanResult.scientificName}</Text>
              </View>
              
              <View style={styles.resultActions}>
                <TouchableOpacity
                  style={[styles.actionButton, isLiked && styles.actionButtonActive]}
                  onPress={() => setIsLiked(!isLiked)}>
                  <Heart
                    size={20}
                    color={isLiked ? '#EF4444' : '#6B7280'}
                    fill={isLiked ? '#EF4444' : 'none'}
                  />
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.actionButton, isBookmarked && styles.actionButtonActive]}
                  onPress={() => setIsBookmarked(!isBookmarked)}>
                  <BookOpen
                    size={20}
                    color={isBookmarked ? '#3B82F6' : '#6B7280'}
                    fill={isBookmarked ? '#3B82F6' : 'none'}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Status Tags */}
            <View style={styles.statusContainer}>
              <View style={styles.statusTag}>
                <Text style={styles.statusLabel}>Category</Text>
                <Text style={styles.statusValue}>{scanResult.category}</Text>
              </View>
              
              <View style={[
                styles.statusTag,
                { backgroundColor: getStatusColor(scanResult.conservationStatus) + '20' }
              ]}>
                <Text style={styles.statusLabel}>Status</Text>
                <Text style={[
                  styles.statusValue,
                  { color: getStatusColor(scanResult.conservationStatus) }
                ]}>
                  {scanResult.conservationStatus}
                </Text>
              </View>
              
              <View style={styles.statusTag}>
                <Text style={styles.statusLabel}>Rarity</Text>
                <Text style={styles.statusValue}>{scanResult.rarity}</Text>
              </View>
            </View>

            {/* Scan Info */}
            <View style={styles.scanInfo}>
              <View style={styles.scanInfoItem}>
                <MapPin size={16} color="#6B7280" />
                <Text style={styles.scanInfoText}>{scanResult.location}</Text>
              </View>
              <View style={styles.scanInfoItem}>
                <Calendar size={16} color="#6B7280" />
                <Text style={styles.scanInfoText}>{scanResult.scanDate}</Text>
              </View>
            </View>
          </View>

          {/* Description */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About</Text>
            <Text style={styles.description}>{scanResult.description}</Text>
          </View>

          {/* Physical Traits */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Physical Traits</Text>
            <View style={styles.traitsGrid}>
              <View style={styles.traitCard}>
                <Ruler size={20} color="#3B82F6" />
                <Text style={styles.traitLabel}>Wingspan</Text>
                <Text style={styles.traitValue}>{scanResult.physicalTraits.wingspan}</Text>
              </View>
              
              <View style={styles.traitCard}>
                <Eye size={20} color="#22C55E" />
                <Text style={styles.traitLabel}>Weight</Text>
                <Text style={styles.traitValue}>{scanResult.physicalTraits.weight}</Text>
              </View>
              
              <View style={styles.traitCard}>
                <Calendar size={20} color="#F59E0B" />
                <Text style={styles.traitLabel}>Lifespan</Text>
                <Text style={styles.traitValue}>{scanResult.physicalTraits.lifespan}</Text>
              </View>
              
              <View style={styles.traitCard}>
                <Leaf size={20} color="#8B5CF6" />
                <Text style={styles.traitLabel}>Diet</Text>
                <Text style={styles.traitValue}>{scanResult.physicalTraits.diet}</Text>
              </View>
            </View>
          </View>

          {/* Habitat */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Habitat & Environment</Text>
            <View style={styles.habitatGrid}>
              <View style={styles.habitatCard}>
                <Thermometer size={18} color="#EF4444" />
                <Text style={styles.habitatLabel}>Temperature</Text>
                <Text style={styles.habitatValue}>{scanResult.habitat.temperature}</Text>
              </View>
              
              <View style={styles.habitatCard}>
                <Droplet size={18} color="#3B82F6" />
                <Text style={styles.habitatLabel}>Humidity</Text>
                <Text style={styles.habitatValue}>{scanResult.habitat.humidity}</Text>
              </View>
              
              <View style={styles.habitatCard}>
                <Sun size={18} color="#F59E0B" />
                <Text style={styles.habitatLabel}>Sunlight</Text>
                <Text style={styles.habitatValue}>{scanResult.habitat.sunlight}</Text>
              </View>
            </View>
          </View>

          {/* Interesting Facts */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Interesting Facts</Text>
            <View style={styles.factsContainer}>
              {scanResult.facts.map((fact, index) => (
                <View key={index} style={styles.factItem}>
                  <Star size={14} color="#F59E0B" />
                  <Text style={styles.factText}>{fact}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Alternative Matches */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Alternative Matches</Text>
            <Text style={styles.sectionSubtitle}>
              Other possible identifications for this specimen
            </Text>
            
            {alternativeMatches.map((match, index) => (
              <TouchableOpacity key={index} style={styles.alternativeCard}>
                <View style={styles.alternativeInfo}>
                  <Text style={styles.alternativeName}>{match.name}</Text>
                  <Text style={styles.alternativeScientific}>{match.scientificName}</Text>
                </View>
                <View style={styles.alternativeConfidence}>
                  <Text style={styles.alternativePercent}>{match.confidence}%</Text>
                  <View style={styles.alternativeBar}>
                    <View
                      style={[
                        styles.alternativeFill,
                        { width: `${match.confidence}%` }
                      ]}
                    />
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtonsContainer}>
            <TouchableOpacity style={styles.primaryActionButton}>
              <LinearGradient
                colors={['#22C55E', '#16A34A']}
                style={styles.primaryButtonGradient}>
                <CheckCircle size={20} color="#FFFFFF" />
                <Text style={styles.primaryButtonText}>Save to Collection</Text>
              </LinearGradient>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.secondaryActionButton}>
              <Volume2 size={20} color="#6B7280" />
              <Text style={styles.secondaryButtonText}>Learn More</Text>
            </TouchableOpacity>
          </View>

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FEF3C7',
  },
  gradient: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 24,
    position: 'relative',
  },
  specimenImage: {
    width: width - 80,
    height: width - 80,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#F59E0B',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  confidenceBadge: {
    position: 'absolute',
    top: 16,
    right: 36,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  confidenceText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  resultCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginHorizontal: 20,
    padding: 24,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  resultTitleContainer: {
    flex: 1,
  },
  resultName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  resultScientific: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    fontStyle: 'italic',
  },
  resultActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonActive: {
    backgroundColor: '#FEE2E2',
  },
  statusContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  statusTag: {
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    alignItems: 'center',
  },
  statusLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginBottom: 2,
  },
  statusValue: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
  },
  scanInfo: {
    gap: 8,
  },
  scanInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  scanInfoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 24,
  },
  traitsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  traitCard: {
    width: (width - 52) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  traitLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginTop: 8,
    marginBottom: 4,
  },
  traitValue: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    textAlign: 'center',
  },
  habitatGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  habitatCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  habitatLabel: {
    fontSize: 11,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginTop: 6,
    marginBottom: 2,
  },
  habitatValue: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    textAlign: 'center',
  },
  factsContainer: {
    gap: 12,
  },
  factItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  factText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#374151',
    lineHeight: 20,
  },
  alternativeCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  alternativeInfo: {
    flex: 1,
  },
  alternativeName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 4,
  },
  alternativeScientific: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    fontStyle: 'italic',
  },
  alternativeConfidence: {
    alignItems: 'flex-end',
    minWidth: 60,
  },
  alternativePercent: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginBottom: 4,
  },
  alternativeBar: {
    width: 50,
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },
  alternativeFill: {
    height: '100%',
    backgroundColor: '#6B7280',
    borderRadius: 2,
  },
  actionButtonsContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  primaryActionButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#22C55E',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  primaryButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 8,
  },
  primaryButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  secondaryActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
  },
  bottomSpacing: {
    height: 100,
  },
});