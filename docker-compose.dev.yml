version: '3.8'

services:
  # Development overrides
  api-gateway:
    volumes:
      - ./backend/api-gateway:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DEBUG=bioscan:*
    command: npm run dev

  auth-service:
    volumes:
      - ./backend/auth-service:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DEBUG=bioscan:auth:*
    command: npm run dev

  media-service:
    volumes:
      - ./backend/media-service:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DEBUG=bioscan:media:*
    command: npm run dev

  notification-service:
    volumes:
      - ./backend/notification-service:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DEBUG=bioscan:notification:*
    command: npm run dev

  payment-service:
    volumes:
      - ./backend/payment-service:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DEBUG=bioscan:payment:*
    command: npm run dev

  analytics-service:
    volumes:
      - ./backend/analytics-service:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - DEBUG=bioscan:analytics:*
    command: npm run dev

  # Development database with exposed ports for debugging
  postgres-auth:
    ports:
      - "5432:5432"

  postgres-media:
    ports:
      - "5433:5432"

  postgres-notifications:
    ports:
      - "5434:5432"

  postgres-payments:
    ports:
      - "5435:5432"

  postgres-analytics:
    ports:
      - "5436:5432"

  # Redis with exposed port for debugging
  redis:
    ports:
      - "6379:6379"

  # Development tools
  pgadmin:
    image: dpage/pgadmin4:latest
    ports:
      - "5050:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - bioscan-network
    restart: unless-stopped

  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:${REDIS_PASSWORD}
    networks:
      - bioscan-network
    restart: unless-stopped

volumes:
  pgadmin_data:
