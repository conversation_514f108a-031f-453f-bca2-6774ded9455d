interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
  }>;
}

interface IdentificationResult {
  name: string;
  scientificName: string;
  confidence: number;
  category: string;
  description: string;
  habitat: string;
  conservationStatus: string;
  facts: string[];
  physicalTraits: {
    size: string;
    weight: string;
    lifespan: string;
    diet: string;
  };
  alternatives: Array<{
    name: string;
    scientificName: string;
    confidence: number;
  }>;
}

export class GeminiService {
  private apiKey: string;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta';

  constructor() {
    this.apiKey = process.env.EXPO_PUBLIC_GEMINI_API_KEY || '';
    if (!this.apiKey) {
      throw new Error('Gemini API key not found. Please check your environment configuration.');
    }
  }

  async identifySpecimen(imageBase64: string, additionalContext?: string): Promise<IdentificationResult> {
    try {
      const prompt = this.buildIdentificationPrompt(additionalContext);
      
      const response = await fetch(`${this.baseUrl}/models/gemini-1.5-flash:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [
              {
                text: prompt
              },
              {
                inline_data: {
                  mime_type: 'image/jpeg',
                  data: imageBase64
                }
              }
            ]
          }],
          generationConfig: {
            temperature: 0.1,
            topK: 32,
            topP: 1,
            maxOutputTokens: 2048,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data: GeminiResponse = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No identification results returned');
      }

      const resultText = data.candidates[0].content.parts[0].text;
      return this.parseIdentificationResult(resultText);
    } catch (error) {
      console.error('Gemini API Error:', error);
      throw new Error('Failed to identify specimen. Please try again later.');
    }
  }

  private buildIdentificationPrompt(additionalContext?: string): string {
    return `
You are an expert biologist and naturalist. Analyze this image and identify the species shown. 
${additionalContext ? `Additional context: ${additionalContext}` : ''}

Please provide a detailed identification in the following JSON format:

{
  "name": "Common name of the species",
  "scientificName": "Scientific name in binomial nomenclature",
  "confidence": 85,
  "category": "Category (e.g., Mammal, Bird, Plant, Insect, etc.)",
  "description": "Detailed description of the species and its characteristics",
  "habitat": "Natural habitat and geographic distribution",
  "conservationStatus": "Conservation status (e.g., Least Concern, Vulnerable, etc.)",
  "facts": [
    "Interesting fact 1",
    "Interesting fact 2",
    "Interesting fact 3"
  ],
  "physicalTraits": {
    "size": "Typical size range",
    "weight": "Typical weight range",
    "lifespan": "Average lifespan",
    "diet": "Primary diet and feeding habits"
  },
  "alternatives": [
    {
      "name": "Alternative species 1",
      "scientificName": "Scientific name",
      "confidence": 15
    },
    {
      "name": "Alternative species 2", 
      "scientificName": "Scientific name",
      "confidence": 10
    }
  ]
}

Important guidelines:
- Be as accurate as possible with the identification
- Provide confidence scores that reflect your certainty
- Include 2-3 alternative possibilities with lower confidence scores
- Ensure all confidence scores add up to approximately 100
- Use proper scientific nomenclature
- Provide practical and educational information
- If you cannot identify the species with reasonable confidence, indicate this clearly

Return only the JSON response, no additional text.
    `.trim();
  }

  private parseIdentificationResult(resultText: string): IdentificationResult {
    try {
      // Clean the response text to extract JSON
      const jsonMatch = resultText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in response');
      }

      const jsonText = jsonMatch[0];
      const parsed = JSON.parse(jsonText);

      // Validate required fields
      if (!parsed.name || !parsed.scientificName || !parsed.confidence) {
        throw new Error('Missing required identification fields');
      }

      return {
        name: parsed.name,
        scientificName: parsed.scientificName,
        confidence: Math.min(100, Math.max(0, parsed.confidence)),
        category: parsed.category || 'Unknown',
        description: parsed.description || 'No description available',
        habitat: parsed.habitat || 'Habitat information not available',
        conservationStatus: parsed.conservationStatus || 'Status unknown',
        facts: Array.isArray(parsed.facts) ? parsed.facts : [],
        physicalTraits: {
          size: parsed.physicalTraits?.size || 'Size unknown',
          weight: parsed.physicalTraits?.weight || 'Weight unknown',
          lifespan: parsed.physicalTraits?.lifespan || 'Lifespan unknown',
          diet: parsed.physicalTraits?.diet || 'Diet unknown',
        },
        alternatives: Array.isArray(parsed.alternatives) ? parsed.alternatives.map((alt: any) => ({
          name: alt.name || 'Unknown',
          scientificName: alt.scientificName || 'Unknown',
          confidence: Math.min(100, Math.max(0, alt.confidence || 0)),
        })) : [],
      };
    } catch (error) {
      console.error('Failed to parse identification result:', error);
      throw new Error('Failed to parse identification results');
    }
  }

  async validateApiKey(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/models?key=${this.apiKey}`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }
}

export default GeminiService;