{"name": "bioscan-app", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"start": "EXPO_NO_TELEMETRY=1 expo start", "dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "expo lint", "lint:fix": "expo lint --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "4.5.6", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.39.0", "expo": "~53.0.0", "expo-auth-session": "~6.2.0", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-crypto": "~14.1.3", "expo-device": "~7.1.3", "expo-file-system": "~18.1.3", "expo-font": "~13.3.1", "expo-haptics": "~14.1.3", "expo-image-manipulator": "~13.1.3", "expo-image-picker": "~16.1.3", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-local-authentication": "^16.0.4", "expo-location": "~18.1.3", "expo-notifications": "~0.31.3", "expo-router": "~5.1.1", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "zxcvbn": "^4.4.2", "@react-native-community/datetimepicker": "8.2.0", "expo-sharing": "~13.1.3", "ajv": "^8.12.0", "ajv-keywords": "^5.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.0", "@types/react": "~19.0.10", "@types/react-dom": "^19.1.6", "@types/zxcvbn": "^4.4.4", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.57.0", "jest": "^29.7.0", "typescript": "~5.8.3"}, "resolutions": {"ajv": "^8.12.0", "ajv-keywords": "^5.1.0"}, "overrides": {"ajv": "^8.12.0", "ajv-keywords": "^5.1.0"}}